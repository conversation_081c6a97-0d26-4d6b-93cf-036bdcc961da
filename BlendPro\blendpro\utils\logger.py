"""
BlendPro Logger
==============

Centralized logging system for BlendPro with different log levels
and optional file output.
"""

import logging
import sys
from pathlib import Path
from typing import Optional
import bpy

# Global logger instance
_logger: Optional[logging.Logger] = None


class BlenderLogHandler(logging.Handler):
    """Custom log handler that outputs to <PERSON>lender's console"""
    
    def emit(self, record):
        """Emit a log record to Blender's console"""
        try:
            msg = self.format(record)
            # Use Blender's print function which goes to console
            print(f"BlendPro: {msg}")
        except Exception:
            self.handleError(record)


def setup_logger(name: str = "BlendPro", level: int = logging.INFO) -> logging.Logger:
    """
    Setup and configure the BlendPro logger
    
    Args:
        name: Logger name
        level: Logging level
        
    Returns:
        Configured logger instance
    """
    global _logger
    
    if _logger is not None:
        return _logger
    
    # Create logger
    _logger = logging.getLogger(name)
    _logger.setLevel(level)
    
    # Prevent duplicate handlers
    if _logger.handlers:
        return _logger
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S'
    )
    
    # Add Blender console handler
    console_handler = BlenderLogHandler()
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    _logger.addHandler(console_handler)
    
    # Optionally add file handler for debugging
    try:
        # Get Blender's temp directory
        temp_dir = Path(bpy.app.tempdir)
        log_file = temp_dir / "blendpro.log"
        
        file_handler = logging.FileHandler(log_file, mode='a', encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        _logger.addHandler(file_handler)
        
        _logger.debug(f"Log file created at: {log_file}")
        
    except Exception as e:
        _logger.warning(f"Could not create log file: {e}")
    
    return _logger


def get_logger(name: str = "BlendPro") -> logging.Logger:
    """
    Get the BlendPro logger instance
    
    Args:
        name: Logger name
        
    Returns:
        Logger instance
    """
    global _logger
    
    if _logger is None:
        return setup_logger(name)
    
    return _logger


def set_log_level(level: int):
    """
    Set the logging level for all handlers
    
    Args:
        level: New logging level
    """
    global _logger
    
    if _logger is not None:
        _logger.setLevel(level)
        for handler in _logger.handlers:
            handler.setLevel(level)


def log_exception(logger: logging.Logger, message: str = "An error occurred"):
    """
    Log an exception with full traceback
    
    Args:
        logger: Logger instance
        message: Custom error message
    """
    logger.exception(message)


# Convenience functions
def debug(message: str):
    """Log a debug message"""
    get_logger().debug(message)


def info(message: str):
    """Log an info message"""
    get_logger().info(message)


def warning(message: str):
    """Log a warning message"""
    get_logger().warning(message)


def error(message: str):
    """Log an error message"""
    get_logger().error(message)


def critical(message: str):
    """Log a critical message"""
    get_logger().critical(message)
