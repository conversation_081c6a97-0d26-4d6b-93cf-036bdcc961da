# Contributing to BlendPro

Thank you for your interest in contributing to BlendPro! This document provides guidelines and information for contributors.

## 🚀 Getting Started

### Prerequisites

- **Blender 4.2+** for testing
- **Python 3.10+** for development
- **Git** for version control
- **Code editor** with Python support (VS Code recommended)

### Development Setup

1. **Clone the repository**:
   ```bash
   git clone https://github.com/inkbytefo/BlendPro.git
   cd BlendPro
   ```

2. **Install development dependencies**:
   ```bash
   pip install -r requirements.txt
   pip install -r requirements-dev.txt  # If available
   ```

3. **Setup Blender development environment**:
   - Install Blender 4.2+
   - Enable Developer Extras in Preferences > Interface
   - Setup external editor for Python development

4. **Install BlendPro in development mode**:
   - Create symlink in Blender addons directory
   - Or use <PERSON><PERSON><PERSON>'s "Install from Disk" with the project folder

## 📋 Development Guidelines

### Code Style

- **PEP 8** compliance for Python code
- **Type hints** for all function parameters and returns
- **Docstrings** for all public functions and classes
- **Consistent naming** conventions:
  - `snake_case` for functions and variables
  - `PascalCase` for classes
  - `UPPER_CASE` for constants

### Project Structure

```
BlendPro/
├── __init__.py              # Main addon entry point
├── blender_manifest.toml    # Extension manifest
├── requirements.txt         # Dependencies
└── blendpro/               # Main package
    ├── ai/                 # AI provider integrations
    │   ├── base.py         # Abstract base classes
    │   ├── openai_provider.py
    │   ├── anthropic_provider.py
    │   ├── google_provider.py
    │   └── manager.py      # AI manager
    ├── core/               # Core functionality
    │   ├── addon_manager.py
    │   └── properties.py
    ├── ui/                 # User interface
    │   ├── panels.py
    │   ├── operators.py
    │   └── preferences.py
    └── utils/              # Utilities
        ├── logger.py
        ├── security.py
        ├── error_handler.py
        └── config.py
```

### Coding Standards

#### 1. Type Hints
```python
def generate_response(
    self, 
    messages: List[AIMessage], 
    config: Optional[AIConfig] = None
) -> AIResponse:
    """Generate AI response with proper typing"""
    pass
```

#### 2. Error Handling
```python
try:
    result = risky_operation()
    return result
except SpecificError as e:
    logger.error(f"Operation failed: {e}")
    raise CustomError(f"Failed to complete operation: {e}")
```

#### 3. Logging
```python
from ..utils.logger import get_logger

class MyClass:
    def __init__(self):
        self.logger = get_logger(f"{__name__}.MyClass")
    
    def my_method(self):
        self.logger.debug("Starting operation")
        # ... operation code ...
        self.logger.info("Operation completed successfully")
```

#### 4. Documentation
```python
def complex_function(param1: str, param2: Optional[int] = None) -> Dict[str, Any]:
    """
    Brief description of what the function does.
    
    Args:
        param1: Description of first parameter
        param2: Description of optional parameter
        
    Returns:
        Description of return value
        
    Raises:
        ValueError: When param1 is invalid
        CustomError: When operation fails
        
    Example:
        >>> result = complex_function("test", 42)
        >>> print(result["status"])
        "success"
    """
    pass
```

## 🧪 Testing

### Running Tests

```bash
# Run all tests
python -m pytest

# Run specific test file
python -m pytest tests/test_ai_providers.py

# Run with coverage
python -m pytest --cov=blendpro

# Run in Blender context (if available)
blender --background --python-expr "import pytest; pytest.main()"
```

### Writing Tests

#### Unit Tests
```python
import pytest
from blendpro.ai.openai_provider import OpenAIProvider

class TestOpenAIProvider:
    def test_provider_initialization(self):
        provider = OpenAIProvider("test-key")
        assert provider.name == "openai"
        assert "gpt-4o" in provider.supported_models
    
    def test_invalid_api_key(self):
        provider = OpenAIProvider("")
        assert not provider.validate_api_key()
```

#### Integration Tests
```python
import pytest
import bpy
from blendpro.core.addon_manager import AddonManager

class TestBlenderIntegration:
    def test_addon_registration(self):
        # Test addon registration in Blender
        assert hasattr(bpy.types.Scene, 'blendpro_settings')
    
    def test_ui_panel_exists(self):
        # Test UI panel registration
        panel = bpy.types.BLENDPRO_PT_main_panel
        assert panel is not None
```

### Test Categories

1. **Unit Tests** (`tests/unit/`):
   - Test individual functions and classes
   - Mock external dependencies
   - Fast execution

2. **Integration Tests** (`tests/integration/`):
   - Test component interactions
   - Test with real Blender API
   - May require API keys for AI providers

3. **UI Tests** (`tests/ui/`):
   - Test Blender UI integration
   - Test operator functionality
   - Require Blender context

4. **Security Tests** (`tests/security/`):
   - Test code validation
   - Test dangerous operation detection
   - Test safe execution environment

## 🐛 Bug Reports

### Before Reporting

1. **Search existing issues** to avoid duplicates
2. **Test with latest version** of BlendPro
3. **Check compatibility** with your Blender version
4. **Gather system information**:
   - Blender version
   - Operating system
   - BlendPro version
   - AI provider being used

### Bug Report Template

```markdown
**Bug Description**
A clear description of the bug.

**Steps to Reproduce**
1. Go to '...'
2. Click on '...'
3. Enter '...'
4. See error

**Expected Behavior**
What you expected to happen.

**Actual Behavior**
What actually happened.

**Screenshots/Logs**
If applicable, add screenshots or log output.

**Environment**
- Blender Version: [e.g., 4.2.0]
- BlendPro Version: [e.g., 1.0.0]
- Operating System: [e.g., Windows 11]
- AI Provider: [e.g., OpenAI GPT-4o]

**Additional Context**
Any other context about the problem.
```

## ✨ Feature Requests

### Before Requesting

1. **Check existing feature requests** and roadmap
2. **Consider if it fits** BlendPro's scope
3. **Think about implementation** complexity

### Feature Request Template

```markdown
**Feature Description**
A clear description of the feature you'd like to see.

**Use Case**
Describe the problem this feature would solve.

**Proposed Solution**
Describe how you envision this feature working.

**Alternatives Considered**
Other solutions you've considered.

**Additional Context**
Any other context, mockups, or examples.
```

## 🔧 Pull Requests

### Before Submitting

1. **Create an issue** to discuss the change
2. **Fork the repository** and create a feature branch
3. **Write tests** for your changes
4. **Update documentation** if needed
5. **Test thoroughly** in Blender

### Pull Request Process

1. **Create feature branch**:
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes** following coding standards

3. **Write/update tests**:
   ```bash
   python -m pytest  # Ensure all tests pass
   ```

4. **Update documentation** if needed

5. **Commit with clear messages**:
   ```bash
   git commit -m "Add: New AI provider support for XYZ"
   ```

6. **Push to your fork**:
   ```bash
   git push origin feature/your-feature-name
   ```

7. **Create pull request** with:
   - Clear title and description
   - Reference to related issue
   - Screenshots if UI changes
   - Test results

### Pull Request Template

```markdown
**Description**
Brief description of changes.

**Related Issue**
Fixes #(issue number)

**Type of Change**
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

**Testing**
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed
- [ ] Tested in Blender 4.2+

**Screenshots**
If applicable, add screenshots.

**Checklist**
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] Tests added/updated
```

## 📚 Documentation

### Types of Documentation

1. **Code Documentation**:
   - Docstrings for all public APIs
   - Type hints for clarity
   - Inline comments for complex logic

2. **User Documentation**:
   - README.md updates
   - Usage examples
   - Troubleshooting guides

3. **Developer Documentation**:
   - Architecture decisions
   - API references
   - Contributing guidelines

### Documentation Standards

- **Clear and concise** language
- **Examples** for complex concepts
- **Screenshots** for UI features
- **Code samples** with proper syntax highlighting
- **Links** to related documentation

## 🏷️ Release Process

### Version Numbering

We follow [Semantic Versioning](https://semver.org/):
- **MAJOR**: Breaking changes
- **MINOR**: New features (backward compatible)
- **PATCH**: Bug fixes (backward compatible)

### Release Checklist

1. **Update version** in `blender_manifest.toml`
2. **Update CHANGELOG.md** with new features/fixes
3. **Run full test suite**
4. **Test in multiple Blender versions**
5. **Create release tag**
6. **Build extension package**
7. **Upload to Extensions platform**
8. **Update documentation**

## 🤝 Community

### Communication Channels

- **GitHub Issues**: Bug reports and feature requests
- **GitHub Discussions**: General questions and ideas
- **Discord**: Real-time chat (if available)
- **Email**: Direct contact for sensitive issues

### Code of Conduct

- **Be respectful** and inclusive
- **Help others** learn and contribute
- **Give constructive feedback**
- **Focus on the code**, not the person
- **Follow project guidelines**

## 📄 License

By contributing to BlendPro, you agree that your contributions will be licensed under the MIT License.

---

Thank you for contributing to BlendPro! 🎉
