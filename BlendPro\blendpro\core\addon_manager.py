"""
BlendPro Addon Manager
=====================

Central manager for BlendPro addon lifecycle, configuration,
and AI provider management.
"""

import bpy
from typing import Optional, Dict, Any
import os
from pathlib import Path

from ..ai.manager import AIManager
from ..ai.base import AIConfig
from ..utils.logger import get_logger
from .properties import get_settings


class AddonManager:
    """Central manager for BlendPro addon"""
    
    def __init__(self):
        self.logger = get_logger(f"{__name__}.AddonManager")
        self.ai_manager = AIManager()
        self._initialized = False
        self._config_cache = {}
        
        # Initialize the addon
        self._initialize()
    
    def _initialize(self):
        """Initialize the addon manager"""
        try:
            self.logger.info("Initializing BlendPro Addon Manager...")
            
            # Load configuration
            self._load_configuration()
            
            # Setup AI providers
            self._setup_ai_providers()
            
            self._initialized = True
            self.logger.info("Addon Manager initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Addon Manager: {e}")
            raise
    
    def _load_configuration(self):
        """Load configuration from preferences"""
        try:
            # Get addon preferences
            prefs = self._get_preferences()
            if not prefs:
                self.logger.warning("No preferences found, using defaults")
                return
            
            # Cache configuration
            self._config_cache = {
                'default_provider': prefs.default_provider,
                'openai_model': prefs.openai_default_model,
                'max_tokens': prefs.default_max_tokens,
                'temperature': prefs.default_temperature,
                'auto_execute': prefs.auto_execute_code,
                'save_history': prefs.save_chat_history,
            }
            
            self.logger.debug("Configuration loaded from preferences")
            
        except Exception as e:
            self.logger.error(f"Failed to load configuration: {e}")
    
    def _setup_ai_providers(self):
        """Setup AI providers with API keys from preferences"""
        try:
            prefs = self._get_preferences()
            if not prefs:
                self.logger.warning("No preferences available for AI setup")
                return
            
            # Setup OpenAI
            if prefs.openai_api_key:
                config = AIConfig(
                    model=prefs.openai_default_model,
                    max_tokens=prefs.default_max_tokens,
                    temperature=prefs.default_temperature
                )
                success = self.ai_manager.register_provider('openai', prefs.openai_api_key, config)
                if success:
                    self.logger.info("OpenAI provider registered successfully")
                else:
                    self.logger.warning("Failed to register OpenAI provider")
            
            # Setup Anthropic
            if prefs.anthropic_api_key:
                config = AIConfig(
                    model=getattr(prefs, 'anthropic_default_model', 'claude-3-5-sonnet-20241022'),
                    max_tokens=prefs.default_max_tokens,
                    temperature=prefs.default_temperature
                )
                success = self.ai_manager.register_provider('anthropic', prefs.anthropic_api_key, config)
                if success:
                    self.logger.info("Anthropic provider registered successfully")
                else:
                    self.logger.warning("Failed to register Anthropic provider")
            
            # Setup Google
            if prefs.google_api_key:
                config = AIConfig(
                    model=getattr(prefs, 'google_default_model', 'gemini-1.5-flash'),
                    max_tokens=prefs.default_max_tokens,
                    temperature=prefs.default_temperature
                )
                success = self.ai_manager.register_provider('google', prefs.google_api_key, config)
                if success:
                    self.logger.info("Google provider registered successfully")
                else:
                    self.logger.warning("Failed to register Google provider")
            
            # Set default active provider
            if prefs.default_provider in self.ai_manager.get_available_providers():
                self.ai_manager.set_active_provider(prefs.default_provider)
                self.logger.info(f"Active provider set to: {prefs.default_provider}")
            
        except Exception as e:
            self.logger.error(f"Failed to setup AI providers: {e}")
    
    def _get_preferences(self):
        """Get addon preferences"""
        try:
            addon_name = "blendpro_ai_assistant"  # Must match manifest id
            if addon_name in bpy.context.preferences.addons:
                return bpy.context.preferences.addons[addon_name].preferences
            return None
        except Exception as e:
            self.logger.error(f"Failed to get preferences: {e}")
            return None
    
    def get_ai_manager(self) -> AIManager:
        """Get the AI manager instance"""
        return self.ai_manager
    
    def is_initialized(self) -> bool:
        """Check if addon manager is initialized"""
        return self._initialized
    
    def reload_configuration(self):
        """Reload configuration from preferences"""
        try:
            self.logger.info("Reloading configuration...")
            self._load_configuration()
            self._setup_ai_providers()
            self.logger.info("Configuration reloaded successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to reload configuration: {e}")
    
    def get_config_value(self, key: str, default=None):
        """Get a configuration value"""
        return self._config_cache.get(key, default)
    
    def update_scene_settings(self, context=None):
        """Update scene settings from preferences"""
        try:
            if context is None:
                context = bpy.context
            
            settings = get_settings(context)
            if not settings:
                self.logger.warning("No scene settings found")
                return
            
            prefs = self._get_preferences()
            if not prefs:
                return
            
            # Update scene settings from preferences
            if settings.ai_provider == 'none' and prefs.default_provider:
                settings.ai_provider = prefs.default_provider
            
            settings.max_tokens = prefs.default_max_tokens
            settings.temperature = prefs.default_temperature
            settings.auto_execute = prefs.auto_execute_code
            settings.save_history = prefs.save_chat_history
            
            self.logger.debug("Scene settings updated from preferences")
            
        except Exception as e:
            self.logger.error(f"Failed to update scene settings: {e}")
    
    def get_available_providers(self) -> list:
        """Get list of available AI providers"""
        return self.ai_manager.get_available_providers()
    
    def get_provider_models(self, provider_name: str) -> list:
        """Get available models for a provider"""
        models = self.ai_manager.get_supported_models(provider_name)
        return models.get(provider_name, [])
    
    def validate_provider_setup(self, provider_name: str) -> bool:
        """Validate that a provider is properly set up"""
        provider = self.ai_manager.get_provider(provider_name)
        if not provider:
            return False
        
        return provider.validate_api_key()
    
    def get_status_info(self) -> Dict[str, Any]:
        """Get status information about the addon"""
        return {
            'initialized': self._initialized,
            'available_providers': self.get_available_providers(),
            'active_provider': self.ai_manager._active_provider,
            'provider_info': self.ai_manager.get_provider_info(),
        }
    
    def cleanup(self):
        """Cleanup addon manager resources"""
        try:
            self.logger.info("Cleaning up Addon Manager...")
            
            # Cleanup AI manager
            if self.ai_manager:
                self.ai_manager.cleanup()
            
            # Clear cache
            self._config_cache.clear()
            self._initialized = False
            
            self.logger.info("Addon Manager cleanup completed")
            
        except Exception as e:
            self.logger.error(f"Error during Addon Manager cleanup: {e}")


# Global addon manager instance
_addon_manager: Optional[AddonManager] = None


def get_addon_manager() -> Optional[AddonManager]:
    """Get the global addon manager instance"""
    global _addon_manager
    return _addon_manager


def initialize_addon_manager() -> AddonManager:
    """Initialize the global addon manager"""
    global _addon_manager
    
    if _addon_manager is None:
        _addon_manager = AddonManager()
    
    return _addon_manager


def cleanup_addon_manager():
    """Cleanup the global addon manager"""
    global _addon_manager
    
    if _addon_manager:
        _addon_manager.cleanup()
        _addon_manager = None
