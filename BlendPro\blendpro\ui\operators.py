"""
BlendPro UI Operators
====================

Modern UI operators for BlendPro using Blender 4.2+ APIs.
"""

import bpy
import asyncio
import threading
from bpy.types import Operator
from bpy.props import StringProperty, BoolProperty, IntProperty

from ..ai.base import AIMessage
from ..ai.manager import AIManager
from ..utils.logger import get_logger


class BlendProExecuteOperator(Operator):
    """Execute AI command and optionally run generated code"""
    
    bl_idname = "blendpro.execute"
    bl_label = "Execute AI Command"
    bl_description = "Generate Blender code using AI and optionally execute it"
    bl_options = {'REGISTER', 'UNDO'}
    
    auto_execute: BoolProperty(
        name="Auto Execute",
        description="Automatically execute generated code",
        default=True
    )
    
    def __init__(self):
        self.logger = get_logger(f"{__name__}.BlendProExecuteOperator")
        self._timer = None
        self._thread = None
        self._result = None
        self._error = None
    
    def modal(self, context, event):
        """Modal execution for async operations"""
        if event.type == 'TIMER':
            # Check if thread is done
            if self._thread and not self._thread.is_alive():
                self._cleanup_timer(context)
                
                if self._error:
                    self.report({'ERROR'}, str(self._error))
                    context.scene.blendpro_settings.last_error = str(self._error)
                    context.scene.blendpro_settings.is_processing = False
                    return {'CANCELLED'}
                
                if self._result:
                    # Add messages to chat history
                    self._add_to_history(context, self._result)
                    
                    # Execute code if requested
                    if self.auto_execute:
                        success = self._execute_code(context, self._result.content)
                        if success:
                            context.scene.blendpro_settings.last_success = True
                            context.scene.blendpro_settings.last_error = ""
                            self.report({'INFO'}, "Code executed successfully")
                        else:
                            self.report({'WARNING'}, "Code generated but execution failed")
                    else:
                        self.report({'INFO'}, "Code generated successfully")
                
                context.scene.blendpro_settings.is_processing = False
                return {'FINISHED'}
        
        elif event.type == 'ESC':
            self._cleanup_timer(context)
            context.scene.blendpro_settings.is_processing = False
            self.report({'INFO'}, "Operation cancelled")
            return {'CANCELLED'}
        
        return {'PASS_THROUGH'}
    
    def invoke(self, context, event):
        """Start the AI generation process"""
        scene = context.scene
        
        # Validate input
        if not hasattr(scene, 'blendpro_chat_input') or not scene.blendpro_chat_input.strip():
            self.report({'ERROR'}, "Please enter a command")
            return {'CANCELLED'}
        
        if not hasattr(scene, 'blendpro_settings'):
            self.report({'ERROR'}, "BlendPro not properly initialized")
            return {'CANCELLED'}
        
        settings = scene.blendpro_settings
        
        # Check if AI provider is configured
        if settings.ai_provider == 'none':
            self.report({'ERROR'}, "Please configure an AI provider in preferences")
            return {'CANCELLED'}
        
        # Set processing state
        settings.is_processing = True
        settings.last_error = ""
        settings.last_success = False
        
        # Start async generation
        self._start_generation(context)
        
        # Setup modal timer
        self._timer = context.window_manager.event_timer_add(0.1, window=context.window)
        context.window_manager.modal_handler_add(self)
        
        return {'RUNNING_MODAL'}
    
    def _start_generation(self, context):
        """Start AI generation in a separate thread"""
        def run_generation():
            try:
                # Get AI manager (this would be injected in real implementation)
                ai_manager = self._get_ai_manager(context)
                
                # Prepare messages
                messages = self._prepare_messages(context)
                
                # Generate response
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                try:
                    self._result = loop.run_until_complete(
                        ai_manager.generate_response(messages)
                    )
                finally:
                    loop.close()
                    
            except Exception as e:
                self.logger.error(f"AI generation failed: {e}")
                self._error = e
        
        self._thread = threading.Thread(target=run_generation)
        self._thread.daemon = True
        self._thread.start()
    
    def _get_ai_manager(self, context):
        """Get AI manager instance (placeholder)"""
        # In real implementation, this would get the manager from addon
        return AIManager()
    
    def _prepare_messages(self, context):
        """Prepare messages for AI generation"""
        messages = []
        
        # Add chat history
        if hasattr(context.scene, 'blendpro_chat_history'):
            for msg in context.scene.blendpro_chat_history[-10:]:  # Last 10 messages
                messages.append(AIMessage(
                    role=msg.type,
                    content=msg.content
                ))
        
        # Add current user message
        messages.append(AIMessage(
            role="user",
            content=context.scene.blendpro_chat_input
        ))
        
        return messages
    
    def _add_to_history(self, context, response):
        """Add messages to chat history"""
        scene = context.scene
        
        # Add user message
        user_msg = scene.blendpro_chat_history.add()
        user_msg.type = 'user'
        user_msg.content = scene.blendpro_chat_input
        
        # Add AI response
        ai_msg = scene.blendpro_chat_history.add()
        ai_msg.type = 'assistant'
        ai_msg.content = response.content
        
        # Clear input
        scene.blendpro_chat_input = ""
        
        # Limit history size
        max_history = getattr(scene.blendpro_settings, 'max_history', 50)
        while len(scene.blendpro_chat_history) > max_history:
            scene.blendpro_chat_history.remove(0)
    
    def _execute_code(self, context, code):
        """Execute the generated code safely"""
        try:
            # Create safe execution environment
            safe_globals = {
                'bpy': bpy,
                '__builtins__': __builtins__,
            }
            
            # Execute code
            exec(code, safe_globals)
            return True
            
        except Exception as e:
            self.logger.error(f"Code execution failed: {e}")
            context.scene.blendpro_settings.last_error = f"Execution error: {str(e)}"
            return False
    
    def _cleanup_timer(self, context):
        """Cleanup modal timer"""
        if self._timer:
            context.window_manager.event_timer_remove(self._timer)
            self._timer = None


class BlendProClearChatOperator(Operator):
    """Clear chat history"""
    
    bl_idname = "blendpro.clear_chat"
    bl_label = "Clear Chat"
    bl_description = "Clear all chat history"
    bl_options = {'REGISTER', 'UNDO'}
    
    def execute(self, context):
        """Clear the chat history"""
        if hasattr(context.scene, 'blendpro_chat_history'):
            context.scene.blendpro_chat_history.clear()
            self.report({'INFO'}, "Chat history cleared")
        
        return {'FINISHED'}


class BlendProShowCodeOperator(Operator):
    """Show generated code in text editor"""
    
    bl_idname = "blendpro.show_code"
    bl_label = "Show Code"
    bl_description = "Show generated code in Blender's text editor"
    bl_options = {'REGISTER'}
    
    code: StringProperty(
        name="Code",
        description="The generated code to show",
        default=""
    )
    
    def execute(self, context):
        """Show code in text editor"""
        if not self.code:
            self.report({'WARNING'}, "No code to show")
            return {'CANCELLED'}
        
        # Create or update text block
        text_name = "BlendPro_Generated_Code.py"
        text_block = bpy.data.texts.get(text_name)
        
        if text_block is None:
            text_block = bpy.data.texts.new(text_name)
        
        text_block.clear()
        text_block.write(self.code)
        
        # Try to open in text editor
        self._open_in_text_editor(context, text_block)
        
        self.report({'INFO'}, f"Code shown in text editor: {text_name}")
        return {'FINISHED'}
    
    def _open_in_text_editor(self, context, text_block):
        """Open text block in text editor"""
        # Find existing text editor or create one
        text_area = None
        
        for area in context.screen.areas:
            if area.type == 'TEXT_EDITOR':
                text_area = area
                break
        
        if text_area is None:
            # Split current area to create text editor
            text_area = self._split_area_to_text_editor(context)
        
        if text_area:
            text_area.spaces.active.text = text_block
    
    def _split_area_to_text_editor(self, context):
        """Split current area to create text editor"""
        try:
            area = context.area
            
            # Split area vertically
            with context.temp_override(area=area):
                bpy.ops.screen.area_split(direction='VERTICAL', factor=0.5)
            
            # Set new area to text editor
            new_area = context.screen.areas[-1]
            new_area.type = 'TEXT_EDITOR'
            
            return new_area
            
        except Exception as e:
            print(f"Failed to split area: {e}")
            return None


class BlendProDeleteMessageOperator(Operator):
    """Delete a message from chat history"""
    
    bl_idname = "blendpro.delete_message"
    bl_label = "Delete Message"
    bl_description = "Delete this message from chat history"
    bl_options = {'REGISTER', 'UNDO'}
    
    message_index: IntProperty(
        name="Message Index",
        description="Index of message to delete",
        default=0
    )
    
    def execute(self, context):
        """Delete the specified message"""
        if hasattr(context.scene, 'blendpro_chat_history'):
            history = context.scene.blendpro_chat_history
            
            if 0 <= self.message_index < len(history):
                history.remove(self.message_index)
                self.report({'INFO'}, "Message deleted")
            else:
                self.report({'WARNING'}, "Invalid message index")
        
        return {'FINISHED'}


class BlendProSettingsOperator(Operator):
    """Open BlendPro settings"""
    
    bl_idname = "blendpro.settings"
    bl_label = "BlendPro Settings"
    bl_description = "Open BlendPro preferences and settings"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        """Open preferences window"""
        bpy.ops.screen.userpref_show('INVOKE_DEFAULT')
        
        # Navigate to add-ons section
        context.preferences.active_section = 'ADDONS'
        
        # Try to find and expand BlendPro addon
        # This is a simplified version - real implementation would search properly
        self.report({'INFO'}, "Opening preferences - navigate to Add-ons > BlendPro")
        
        return {'FINISHED'}
