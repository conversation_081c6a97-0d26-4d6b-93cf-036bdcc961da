"""
BlendPro - Modern AI-Powered Blender Assistant
==============================================

A modern refactor of BlenderGPT-reference with:
- Latest OpenAI API v1+ support
- Multi-AI model support (<PERSON>T<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>)
- Modern Blender 4.2+ Extensions platform compatibility
- Enhanced UI/UX with modern Blender APIs
- Robust error handling and security features

Author: inkbytefo
License: MIT
Version: 1.0.0
"""

import sys
import os
from pathlib import Path

# Add the blendpro package to Python path
addon_dir = Path(__file__).parent
blendpro_path = addon_dir / "blendpro"
if str(blendpro_path) not in sys.path:
    sys.path.insert(0, str(blendpro_path))

import bpy
import bpy.props
import bpy.types
from bpy.app.handlers import persistent

# Import BlendPro modules
try:
    from blendpro.core.addon_manager import AddonManager
    from blendpro.ui.panels import BlendProPanel
    from blendpro.ui.operators import (
        BlendProExecuteOperator,
        BlendProClearChatOperator,
        BlendProShowCodeOperator,
        BlendProDeleteMessageOperator,
        BlendProSettingsOperator
    )
    from blendpro.ui.preferences import BlendProPreferences
    from blendpro.core.properties import init_properties, clear_properties
    from blendpro.utils.logger import get_logger
except ImportError as e:
    print(f"BlendPro: Failed to import modules: {e}")
    # Fallback for development
    pass

# Addon metadata
bl_info = {
    "name": "BlendPro AI Assistant",
    "blender": (4, 2, 0),
    "category": "3D View",
    "author": "inkbytefo",
    "version": (1, 0, 0),
    "location": "3D View > Sidebar > BlendPro",
    "description": "Modern AI-powered Blender assistant with multi-model support",
    "warning": "",
    "doc_url": "https://github.com/inkbytefo/BlendPro",
    "tracker_url": "https://github.com/inkbytefo/BlendPro/issues",
    "support": "COMMUNITY",
}

# Global addon manager instance
addon_manager = None
logger = None

# Classes to register
classes = [
    BlendProPreferences,
    BlendProExecuteOperator,
    BlendProClearChatOperator,
    BlendProShowCodeOperator,
    BlendProDeleteMessageOperator,
    BlendProSettingsOperator,
    BlendProPanel,
]


@persistent
def load_post_handler(dummy):
    """Handler called after loading a .blend file"""
    global logger
    if logger:
        logger.info("BlendPro: Blend file loaded")


@persistent
def save_pre_handler(dummy):
    """Handler called before saving a .blend file"""
    global logger
    if logger:
        logger.info("BlendPro: Saving blend file")


def register():
    """Register all BlendPro classes and initialize the addon"""
    global addon_manager, logger
    
    try:
        # Initialize logger
        logger = get_logger(__name__)
        logger.info("BlendPro: Starting registration...")
        
        # Register all classes
        for cls in classes:
            try:
                bpy.utils.register_class(cls)
                logger.debug(f"BlendPro: Registered class {cls.__name__}")
            except Exception as e:
                logger.error(f"BlendPro: Failed to register class {cls.__name__}: {e}")
                raise
        
        # Initialize properties
        init_properties()
        logger.debug("BlendPro: Properties initialized")
        
        # Initialize addon manager
        addon_manager = AddonManager()
        logger.debug("BlendPro: Addon manager initialized")
        
        # Register handlers
        bpy.app.handlers.load_post.append(load_post_handler)
        bpy.app.handlers.save_pre.append(save_pre_handler)
        logger.debug("BlendPro: Handlers registered")
        
        logger.info("BlendPro: Registration completed successfully")
        
    except Exception as e:
        logger.error(f"BlendPro: Registration failed: {e}")
        # Attempt cleanup on failure
        try:
            unregister()
        except:
            pass
        raise


def unregister():
    """Unregister all BlendPro classes and cleanup"""
    global addon_manager, logger
    
    try:
        if logger:
            logger.info("BlendPro: Starting unregistration...")
        
        # Remove handlers
        if load_post_handler in bpy.app.handlers.load_post:
            bpy.app.handlers.load_post.remove(load_post_handler)
        if save_pre_handler in bpy.app.handlers.save_pre:
            bpy.app.handlers.save_pre.remove(save_pre_handler)
        
        # Cleanup addon manager
        if addon_manager:
            addon_manager.cleanup()
            addon_manager = None
        
        # Clear properties
        clear_properties()
        
        # Unregister classes in reverse order
        for cls in reversed(classes):
            try:
                bpy.utils.unregister_class(cls)
                if logger:
                    logger.debug(f"BlendPro: Unregistered class {cls.__name__}")
            except Exception as e:
                if logger:
                    logger.error(f"BlendPro: Failed to unregister class {cls.__name__}: {e}")
        
        if logger:
            logger.info("BlendPro: Unregistration completed")
            
    except Exception as e:
        if logger:
            logger.error(f"BlendPro: Unregistration failed: {e}")
        print(f"BlendPro: Unregistration failed: {e}")


if __name__ == "__main__":
    register()
