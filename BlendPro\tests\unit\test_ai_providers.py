"""
Unit Tests for AI Providers
===========================

Test AI provider implementations without making actual API calls.
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
import asyncio

from blendpro.ai.base import AIMessage, AIResponse, AIConfig, AIError
from blendpro.ai.openai_provider import OpenAIProvider
from blendpro.ai.anthropic_provider import AnthropicProvider
from blendpro.ai.google_provider import GoogleProvider
from blendpro.ai.manager import AIManager


class TestAIBase:
    """Test base AI classes and structures"""
    
    def test_ai_message_creation(self):
        """Test AIMessage creation"""
        message = AIMessage(role="user", content="Test message")
        assert message.role == "user"
        assert message.content == "Test message"
        assert message.timestamp is not None
    
    def test_ai_response_creation(self):
        """Test AIResponse creation"""
        response = AIResponse(
            content="Generated code",
            model="gpt-4o",
            provider="openai"
        )
        assert response.content == "Generated code"
        assert response.model == "gpt-4o"
        assert response.provider == "openai"
        assert response.timestamp is not None
    
    def test_ai_config_creation(self):
        """Test AIConfig creation"""
        config = AIConfig(
            model="gpt-4o",
            max_tokens=1000,
            temperature=0.5
        )
        assert config.model == "gpt-4o"
        assert config.max_tokens == 1000
        assert config.temperature == 0.5


class TestOpenAIProvider:
    """Test OpenAI provider implementation"""
    
    def test_provider_initialization(self):
        """Test provider initialization"""
        with patch('blendpro.ai.openai_provider.OPENAI_AVAILABLE', True):
            provider = OpenAIProvider("test-key")
            assert provider.name == "openai"
            assert provider.api_key == "test-key"
            assert "gpt-4o" in provider.supported_models
    
    def test_provider_without_openai_library(self):
        """Test provider when OpenAI library not available"""
        with patch('blendpro.ai.openai_provider.OPENAI_AVAILABLE', False):
            with pytest.raises(Exception):
                OpenAIProvider("test-key")
    
    def test_model_support_check(self):
        """Test model support checking"""
        with patch('blendpro.ai.openai_provider.OPENAI_AVAILABLE', True):
            provider = OpenAIProvider("test-key")
            assert provider.is_model_supported("gpt-4o")
            assert provider.is_model_supported("gpt-3.5-turbo")
            assert not provider.is_model_supported("invalid-model")
    
    @pytest.mark.asyncio
    async def test_generate_response_mock(self):
        """Test response generation with mocked API"""
        with patch('blendpro.ai.openai_provider.OPENAI_AVAILABLE', True):
            with patch('blendpro.ai.openai_provider.AsyncOpenAI') as mock_openai:
                # Setup mock
                mock_client = AsyncMock()
                mock_openai.return_value = mock_client
                
                mock_response = Mock()
                mock_response.choices = [Mock()]
                mock_response.choices[0].message.content = "import bpy\nbpy.ops.mesh.primitive_cube_add()"
                mock_response.choices[0].finish_reason = "stop"
                mock_response.usage.prompt_tokens = 10
                mock_response.usage.completion_tokens = 20
                mock_response.usage.total_tokens = 30
                
                mock_client.chat.completions.create.return_value = mock_response
                
                # Test
                provider = OpenAIProvider("test-key")
                messages = [AIMessage(role="user", content="Create a cube")]
                
                response = await provider.generate_response(messages)
                
                assert response.content == "import bpy\nbpy.ops.mesh.primitive_cube_add()"
                assert response.model == "gpt-4o-mini"  # default model
                assert response.provider == "openai"
                assert response.usage["total_tokens"] == 30
    
    def test_code_extraction(self):
        """Test code extraction from markdown"""
        with patch('blendpro.ai.openai_provider.OPENAI_AVAILABLE', True):
            provider = OpenAIProvider("test-key")
            
            # Test with markdown code blocks
            content_with_markdown = """```python
import bpy
bpy.ops.mesh.primitive_cube_add()
```"""
            extracted = provider._extract_code(content_with_markdown)
            assert "import bpy" in extracted
            assert "```" not in extracted
            
            # Test without markdown
            content_without_markdown = "import bpy\nbpy.ops.mesh.primitive_cube_add()"
            extracted = provider._extract_code(content_without_markdown)
            assert extracted == content_without_markdown


class TestAnthropicProvider:
    """Test Anthropic provider implementation"""
    
    def test_provider_initialization(self):
        """Test provider initialization"""
        with patch('blendpro.ai.anthropic_provider.ANTHROPIC_AVAILABLE', True):
            provider = AnthropicProvider("test-key")
            assert provider.name == "anthropic"
            assert provider.api_key == "test-key"
            assert "claude-3-5-sonnet-20241022" in provider.supported_models
    
    def test_message_conversion(self):
        """Test message conversion to Anthropic format"""
        with patch('blendpro.ai.anthropic_provider.ANTHROPIC_AVAILABLE', True):
            provider = AnthropicProvider("test-key")
            
            messages = [
                AIMessage(role="system", content="You are a helpful assistant"),
                AIMessage(role="user", content="Create a cube"),
                AIMessage(role="assistant", content="import bpy")
            ]
            
            system_prompt, anthropic_messages = provider._convert_messages(messages)
            
            assert system_prompt == "You are a helpful assistant"
            assert len(anthropic_messages) == 2
            assert anthropic_messages[0]["role"] == "user"
            assert anthropic_messages[1]["role"] == "assistant"


class TestGoogleProvider:
    """Test Google provider implementation"""
    
    def test_provider_initialization(self):
        """Test provider initialization"""
        with patch('blendpro.ai.google_provider.GOOGLE_AVAILABLE', True):
            with patch('blendpro.ai.google_provider.genai') as mock_genai:
                provider = GoogleProvider("test-key")
                assert provider.name == "google"
                assert provider.api_key == "test-key"
                assert "gemini-1.5-pro" in provider.supported_models
                mock_genai.configure.assert_called_once_with(api_key="test-key")


class TestAIManager:
    """Test AI manager functionality"""
    
    def test_manager_initialization(self):
        """Test manager initialization"""
        manager = AIManager()
        assert len(manager.get_available_providers()) == 0
        assert manager.get_active_provider() is None
    
    def test_provider_registration(self):
        """Test provider registration"""
        manager = AIManager()
        
        with patch('blendpro.ai.openai_provider.OPENAI_AVAILABLE', True):
            with patch.object(OpenAIProvider, 'validate_api_key', return_value=True):
                success = manager.register_provider("openai", "test-key")
                assert success
                assert "openai" in manager.get_available_providers()
                assert manager.get_active_provider() is not None
    
    def test_provider_registration_invalid_key(self):
        """Test provider registration with invalid key"""
        manager = AIManager()
        
        with patch('blendpro.ai.openai_provider.OPENAI_AVAILABLE', True):
            with patch.object(OpenAIProvider, 'validate_api_key', return_value=False):
                success = manager.register_provider("openai", "invalid-key")
                assert not success
                assert len(manager.get_available_providers()) == 0
    
    def test_active_provider_management(self):
        """Test active provider management"""
        manager = AIManager()
        
        with patch('blendpro.ai.openai_provider.OPENAI_AVAILABLE', True):
            with patch.object(OpenAIProvider, 'validate_api_key', return_value=True):
                manager.register_provider("openai", "test-key")
                
                # Should be active by default (first provider)
                assert manager._active_provider == "openai"
                
                # Test setting active provider
                success = manager.set_active_provider("openai")
                assert success
                
                # Test setting non-existent provider
                success = manager.set_active_provider("nonexistent")
                assert not success
    
    def test_supported_models_retrieval(self):
        """Test supported models retrieval"""
        manager = AIManager()
        
        with patch('blendpro.ai.openai_provider.OPENAI_AVAILABLE', True):
            with patch.object(OpenAIProvider, 'validate_api_key', return_value=True):
                manager.register_provider("openai", "test-key")
                
                models = manager.get_supported_models("openai")
                assert "openai" in models
                assert "gpt-4o" in models["openai"]
                
                all_models = manager.get_supported_models()
                assert "openai" in all_models
    
    @pytest.mark.asyncio
    async def test_generate_response_no_provider(self):
        """Test response generation with no provider"""
        manager = AIManager()
        messages = [AIMessage(role="user", content="test")]
        
        with pytest.raises(Exception):
            await manager.generate_response(messages)
    
    def test_cleanup(self):
        """Test manager cleanup"""
        manager = AIManager()
        
        with patch('blendpro.ai.openai_provider.OPENAI_AVAILABLE', True):
            with patch.object(OpenAIProvider, 'validate_api_key', return_value=True):
                manager.register_provider("openai", "test-key")
                assert len(manager.get_available_providers()) == 1
                
                manager.cleanup()
                assert len(manager.get_available_providers()) == 0
                assert manager.get_active_provider() is None


if __name__ == "__main__":
    pytest.main([__file__])
