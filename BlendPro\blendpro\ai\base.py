"""
Base AI Provider Classes
=======================

Abstract base classes and common structures for AI providers.
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import List, Dict, Any, Optional, AsyncGenerator
from enum import Enum
import time


class AIError(Exception):
    """Base exception for AI-related errors"""
    pass


class AIProviderError(AIError):
    """Error specific to AI provider operations"""
    pass


class AIRateLimitError(AIError):
    """Error when rate limit is exceeded"""
    pass


class AITimeoutError(AIError):
    """Error when request times out"""
    pass


class ModelType(Enum):
    """Supported AI model types"""
    GPT_4O = "gpt-4o"
    GPT_4O_MINI = "gpt-4o-mini"
    GPT_4_TURBO = "gpt-4-turbo"
    GPT_35_TURBO = "gpt-3.5-turbo"
    CLAUDE_35_SONNET = "claude-3-5-sonnet-20241022"
    CLAUDE_3_HAIKU = "claude-3-haiku-20240307"
    GEMINI_15_PRO = "gemini-1.5-pro"
    GEMINI_15_FLASH = "gemini-1.5-flash"


@dataclass
class AIMessage:
    """Represents a message in the conversation"""
    role: str  # "system", "user", "assistant"
    content: str
    timestamp: Optional[float] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()


@dataclass
class AIResponse:
    """Response from AI provider"""
    content: str
    model: str
    provider: str
    usage: Optional[Dict[str, Any]] = None
    finish_reason: Optional[str] = None
    timestamp: Optional[float] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()


@dataclass
class AIConfig:
    """Configuration for AI requests"""
    model: str
    max_tokens: int = 2000
    temperature: float = 0.1
    timeout: int = 30
    stream: bool = False
    system_prompt: Optional[str] = None


class AIProvider(ABC):
    """Abstract base class for AI providers"""
    
    def __init__(self, api_key: str, config: Optional[AIConfig] = None):
        self.api_key = api_key
        self.config = config or AIConfig(model="default")
        self._client = None
        
    @property
    @abstractmethod
    def name(self) -> str:
        """Provider name"""
        pass
    
    @property
    @abstractmethod
    def supported_models(self) -> List[str]:
        """List of supported model names"""
        pass
    
    @abstractmethod
    def validate_api_key(self) -> bool:
        """Validate the API key"""
        pass
    
    @abstractmethod
    async def generate_response(
        self, 
        messages: List[AIMessage], 
        config: Optional[AIConfig] = None
    ) -> AIResponse:
        """Generate a response from the AI model"""
        pass
    
    @abstractmethod
    async def generate_stream(
        self, 
        messages: List[AIMessage], 
        config: Optional[AIConfig] = None
    ) -> AsyncGenerator[str, None]:
        """Generate a streaming response from the AI model"""
        pass
    
    def is_model_supported(self, model: str) -> bool:
        """Check if a model is supported by this provider"""
        return model in self.supported_models
    
    def get_default_config(self) -> AIConfig:
        """Get default configuration for this provider"""
        return self.config
    
    def update_config(self, **kwargs):
        """Update configuration parameters"""
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)


# System prompt for Blender code generation
BLENDER_SYSTEM_PROMPT = """You are an expert Blender Python API assistant. Your role is to generate clean, efficient, and safe Blender Python code based on user requests.

IMPORTANT GUIDELINES:
1. ONLY respond with Python code - no explanations, comments, or markdown
2. Use proper Blender Python API (bpy) syntax
3. Import required modules at the top (bpy, bmesh, mathutils, etc.)
4. Handle errors gracefully with try-except blocks when appropriate
5. Do not perform destructive operations without explicit user request
6. Do not modify render settings, cameras, or lighting unless specifically asked
7. Use meaningful variable names and follow Python conventions
8. Ensure code is compatible with Blender 4.2+

EXAMPLE:
User: "Create a cube at position (2, 0, 0)"
Response:
```python
import bpy

# Delete default cube if it exists
if "Cube" in bpy.data.objects:
    bpy.data.objects.remove(bpy.data.objects["Cube"], do_unlink=True)

# Create new cube at specified position
bpy.ops.mesh.primitive_cube_add(location=(2, 0, 0))
```

Remember: Only output executable Python code, nothing else."""
