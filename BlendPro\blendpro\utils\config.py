"""
BlendPro Configuration
=====================

Configuration management for BlendPro including model definitions,
provider settings, and feature flags.
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum


class ProviderType(Enum):
    """Supported AI providers"""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"


@dataclass
class ModelInfo:
    """Information about an AI model"""
    id: str
    name: str
    description: str
    provider: ProviderType
    max_tokens: int
    supports_streaming: bool = True
    cost_per_1k_tokens: Optional[float] = None
    context_window: Optional[int] = None


# Model definitions with latest information
MODEL_REGISTRY: Dict[str, ModelInfo] = {
    # OpenAI Models
    "gpt-4o": ModelInfo(
        id="gpt-4o",
        name="GPT-4o",
        description="Latest GPT-4o model with improved performance",
        provider=ProviderType.OPENAI,
        max_tokens=4096,
        supports_streaming=True,
        cost_per_1k_tokens=0.015,
        context_window=128000
    ),
    "gpt-4o-mini": ModelInfo(
        id="gpt-4o-mini",
        name="GPT-4o Mini",
        description="Faster, more affordable GPT-4o variant",
        provider=ProviderType.OPENAI,
        max_tokens=4096,
        supports_streaming=True,
        cost_per_1k_tokens=0.0015,
        context_window=128000
    ),
    "gpt-4-turbo": ModelInfo(
        id="gpt-4-turbo",
        name="GPT-4 Turbo",
        description="High-performance GPT-4 model",
        provider=ProviderType.OPENAI,
        max_tokens=4096,
        supports_streaming=True,
        cost_per_1k_tokens=0.01,
        context_window=128000
    ),
    "gpt-3.5-turbo": ModelInfo(
        id="gpt-3.5-turbo",
        name="GPT-3.5 Turbo",
        description="Fast and efficient GPT-3.5 model",
        provider=ProviderType.OPENAI,
        max_tokens=4096,
        supports_streaming=True,
        cost_per_1k_tokens=0.0005,
        context_window=16385
    ),
    
    # Anthropic Models
    "claude-3-5-sonnet-20241022": ModelInfo(
        id="claude-3-5-sonnet-20241022",
        name="Claude 3.5 Sonnet",
        description="Latest Claude 3.5 Sonnet with enhanced capabilities",
        provider=ProviderType.ANTHROPIC,
        max_tokens=4096,
        supports_streaming=True,
        cost_per_1k_tokens=0.003,
        context_window=200000
    ),
    "claude-3-haiku-20240307": ModelInfo(
        id="claude-3-haiku-20240307",
        name="Claude 3 Haiku",
        description="Fast and efficient Claude 3 model",
        provider=ProviderType.ANTHROPIC,
        max_tokens=4096,
        supports_streaming=True,
        cost_per_1k_tokens=0.00025,
        context_window=200000
    ),
    
    # Google Models
    "gemini-1.5-pro": ModelInfo(
        id="gemini-1.5-pro",
        name="Gemini 1.5 Pro",
        description="Advanced Gemini model with large context window",
        provider=ProviderType.GOOGLE,
        max_tokens=8192,
        supports_streaming=True,
        cost_per_1k_tokens=0.0035,
        context_window=2000000
    ),
    "gemini-1.5-flash": ModelInfo(
        id="gemini-1.5-flash",
        name="Gemini 1.5 Flash",
        description="Fast and efficient Gemini model",
        provider=ProviderType.GOOGLE,
        max_tokens=8192,
        supports_streaming=True,
        cost_per_1k_tokens=0.00035,
        context_window=1000000
    ),
}


class BlendProConfig:
    """Central configuration for BlendPro"""
    
    # Default settings
    DEFAULT_PROVIDER = ProviderType.OPENAI
    DEFAULT_MODEL = "gpt-4o-mini"
    DEFAULT_MAX_TOKENS = 2000
    DEFAULT_TEMPERATURE = 0.1
    DEFAULT_TIMEOUT = 30
    
    # UI Settings
    DEFAULT_AUTO_EXECUTE = False
    DEFAULT_SAVE_HISTORY = True
    DEFAULT_MAX_HISTORY = 50
    DEFAULT_SHOW_ADVANCED = False
    
    # Security Settings
    DEFAULT_ENABLE_CODE_REVIEW = True
    DEFAULT_RESTRICT_DANGEROUS_OPS = True
    
    # Feature Flags
    ENABLE_STREAMING = True
    ENABLE_MULTI_PROVIDER = True
    ENABLE_MODEL_SWITCHING = True
    ENABLE_COST_TRACKING = False  # Future feature
    ENABLE_USAGE_ANALYTICS = False  # Future feature
    
    @classmethod
    def get_models_for_provider(cls, provider: ProviderType) -> List[ModelInfo]:
        """Get all models for a specific provider"""
        return [model for model in MODEL_REGISTRY.values() if model.provider == provider]
    
    @classmethod
    def get_model_info(cls, model_id: str) -> Optional[ModelInfo]:
        """Get information about a specific model"""
        return MODEL_REGISTRY.get(model_id)
    
    @classmethod
    def get_recommended_model(cls, provider: ProviderType) -> Optional[str]:
        """Get recommended model for a provider"""
        recommendations = {
            ProviderType.OPENAI: "gpt-4o-mini",
            ProviderType.ANTHROPIC: "claude-3-5-sonnet-20241022", 
            ProviderType.GOOGLE: "gemini-1.5-flash",
        }
        return recommendations.get(provider)
    
    @classmethod
    def get_provider_capabilities(cls, provider: ProviderType) -> Dict[str, Any]:
        """Get capabilities for a provider"""
        capabilities = {
            ProviderType.OPENAI: {
                "streaming": True,
                "function_calling": True,
                "vision": True,
                "json_mode": True,
                "max_context": 128000,
            },
            ProviderType.ANTHROPIC: {
                "streaming": True,
                "function_calling": False,
                "vision": True,
                "json_mode": False,
                "max_context": 200000,
            },
            ProviderType.GOOGLE: {
                "streaming": True,
                "function_calling": True,
                "vision": True,
                "json_mode": True,
                "max_context": 2000000,
            },
        }
        return capabilities.get(provider, {})
    
    @classmethod
    def validate_model_config(cls, provider: str, model: str, max_tokens: int) -> bool:
        """Validate model configuration"""
        model_info = cls.get_model_info(model)
        if not model_info:
            return False
        
        if model_info.provider.value != provider:
            return False
        
        if max_tokens > model_info.max_tokens:
            return False
        
        return True


# System prompts for different use cases
SYSTEM_PROMPTS = {
    "default": """You are an expert Blender Python API assistant. Generate clean, efficient, and safe Blender Python code based on user requests.

IMPORTANT GUIDELINES:
1. ONLY respond with Python code - no explanations, comments, or markdown
2. Use proper Blender Python API (bpy) syntax
3. Import required modules at the top (bpy, bmesh, mathutils, etc.)
4. Handle errors gracefully with try-except blocks when appropriate
5. Do not perform destructive operations without explicit user request
6. Do not modify render settings, cameras, or lighting unless specifically asked
7. Use meaningful variable names and follow Python conventions
8. Ensure code is compatible with Blender 4.2+""",
    
    "modeling": """You are a Blender modeling expert. Generate Python code for 3D modeling operations using the Blender API.

Focus on:
- Mesh creation and modification
- Modifier operations
- Geometry nodes
- UV mapping
- Vertex groups and weight painting

Always use proper error handling and validate selections before operations.""",
    
    "animation": """You are a Blender animation expert. Generate Python code for animation and rigging operations.

Focus on:
- Keyframe animation
- Armature and bone operations
- Constraints
- Drivers
- Animation curves and modifiers

Ensure proper frame range handling and animation data validation.""",
    
    "materials": """You are a Blender materials and shading expert. Generate Python code for material and shader operations.

Focus on:
- Material creation and assignment
- Shader node operations
- Texture handling
- Lighting setup
- Render settings

Use proper material slot management and node tree operations.""",
}


def get_system_prompt(prompt_type: str = "default") -> str:
    """Get system prompt for specific use case"""
    return SYSTEM_PROMPTS.get(prompt_type, SYSTEM_PROMPTS["default"])


def get_blender_code_template() -> str:
    """Get basic Blender code template"""
    return """import bpy
import bmesh
from mathutils import Vector, Matrix

# Your generated code here
"""
