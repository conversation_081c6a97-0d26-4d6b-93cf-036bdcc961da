schema_version = "1.0.0"

# BlendPro - Modern AI-Powered Blender Assistant
# Refactored from BlenderGPT-reference with latest technologies
id = "blendpro_ai_assistant"
version = "1.0.0"
name = "BlendPro AI Assistant"
tagline = "Modern AI-powered Blender assistant with multi-model support"
maintainer = "inkbytefo"
type = "add-on"

# Documentation and support
website = "https://github.com/inkbytefo/BlendPro"

# Tags for discoverability
tags = ["3D View", "Development", "Modeling", "Animation"]

# Blender version compatibility
blender_version_min = "4.2.0"

# License
license = [
  "SPDX:MIT",
]
copyright = [
  "2025 inkbytefo",
]

# Platform support - all platforms
platforms = ["windows-x64", "windows-arm64", "macos-x64", "macos-arm64", "linux-x64"]

# Required permissions
[permissions]
network = "Connect to AI services (OpenAI, Anthropic, Google) for code generation"
files = "Save and load AI-generated code snippets and project templates"
clipboard = "Copy generated code to clipboard for easy sharing"

# Build configuration
[build]
paths_exclude_pattern = [
  "__pycache__/",
  "/.git/",
  "*.zip",
  "*.pyc",
  ".env",
  "tests/",
  "docs/",
  ".vscode/",
  ".idea/",
]
