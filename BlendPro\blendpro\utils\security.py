"""
BlendPro Security
================

Security utilities for BlendPro including code validation,
safe execution, and dangerous operation detection.
"""

import ast
import re
import sys
from typing import List, Dict, Set, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from .logger import get_logger


class SecurityLevel(Enum):
    """Security levels for code execution"""
    STRICT = "strict"      # Block all potentially dangerous operations
    MODERATE = "moderate"  # Warn about dangerous operations but allow
    PERMISSIVE = "permissive"  # Allow all operations with minimal checks


class RiskLevel(Enum):
    """Risk levels for operations"""
    SAFE = "safe"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class SecurityIssue:
    """Represents a security issue found in code"""
    risk_level: RiskLevel
    message: str
    line_number: Optional[int] = None
    suggestion: Optional[str] = None


class CodeValidator:
    """Validates Python code for security issues"""
    
    # Dangerous modules and functions
    DANGEROUS_MODULES = {
        'os', 'sys', 'subprocess', 'shutil', 'glob', 'pathlib',
        'urllib', 'requests', 'socket', 'ftplib', 'smtplib',
        'pickle', 'marshal', 'shelve', 'dbm', 'sqlite3',
        'ctypes', 'multiprocessing', 'threading', 'asyncio',
        'importlib', '__import__', 'eval', 'exec', 'compile'
    }
    
    # Dangerous function patterns
    DANGEROUS_PATTERNS = [
        r'bpy\.ops\.wm\.quit.*',  # Quit Blender
        r'bpy\.ops\.wm\.save.*',  # Save operations
        r'bpy\.ops\.export.*',    # Export operations
        r'bpy\.ops\.import.*',    # Import operations
        r'bpy\.data\..*\.remove.*',  # Data removal
        r'bpy\.context\.preferences.*',  # Preference modifications
        r'open\s*\(',            # File operations
        r'file\s*\(',            # File operations
        r'input\s*\(',           # User input
        r'raw_input\s*\(',       # User input (Python 2)
        r'__.*__',               # Dunder methods
    ]
    
    # Safe Blender operations
    SAFE_BPY_OPERATIONS = {
        'bpy.ops.mesh.',
        'bpy.ops.object.',
        'bpy.ops.material.',
        'bpy.ops.node.',
        'bpy.ops.anim.',
        'bpy.ops.pose.',
        'bpy.ops.armature.',
        'bpy.ops.curve.',
        'bpy.ops.surface.',
        'bpy.ops.metaball.',
        'bpy.ops.lattice.',
        'bpy.ops.camera.',
        'bpy.ops.lamp.',
        'bpy.ops.light.',
        'bpy.ops.speaker.',
        'bpy.ops.text.',
        'bpy.ops.font.',
        'bpy.ops.gpencil.',
        'bpy.ops.paint.',
        'bpy.ops.sculpt.',
        'bpy.ops.uv.',
        'bpy.ops.transform.',
        'bpy.ops.view3d.',
        'bpy.ops.screen.',
        'bpy.ops.render.',
    }
    
    def __init__(self, security_level: SecurityLevel = SecurityLevel.MODERATE):
        self.security_level = security_level
        self.logger = get_logger(f"{__name__}.CodeValidator")
    
    def validate_code(self, code: str) -> Tuple[bool, List[SecurityIssue]]:
        """
        Validate code for security issues
        
        Args:
            code: Python code to validate
            
        Returns:
            Tuple of (is_safe, list_of_issues)
        """
        issues = []
        
        try:
            # Parse the code into AST
            tree = ast.parse(code)
            
            # Check for dangerous imports
            issues.extend(self._check_imports(tree))
            
            # Check for dangerous function calls
            issues.extend(self._check_function_calls(tree))
            
            # Check for dangerous patterns
            issues.extend(self._check_patterns(code))
            
            # Check for file operations
            issues.extend(self._check_file_operations(tree))
            
            # Check for network operations
            issues.extend(self._check_network_operations(tree))
            
            # Determine if code is safe based on security level
            is_safe = self._determine_safety(issues)
            
            return is_safe, issues
            
        except SyntaxError as e:
            issues.append(SecurityIssue(
                risk_level=RiskLevel.HIGH,
                message=f"Syntax error in code: {e}",
                line_number=e.lineno,
                suggestion="Fix syntax errors before execution"
            ))
            return False, issues
        
        except Exception as e:
            self.logger.error(f"Error validating code: {e}")
            issues.append(SecurityIssue(
                risk_level=RiskLevel.CRITICAL,
                message=f"Code validation failed: {e}",
                suggestion="Code cannot be validated for safety"
            ))
            return False, issues
    
    def _check_imports(self, tree: ast.AST) -> List[SecurityIssue]:
        """Check for dangerous imports"""
        issues = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    if alias.name in self.DANGEROUS_MODULES:
                        issues.append(SecurityIssue(
                            risk_level=RiskLevel.HIGH,
                            message=f"Dangerous module import: {alias.name}",
                            line_number=node.lineno,
                            suggestion=f"Remove import of {alias.name} or use safer alternatives"
                        ))
            
            elif isinstance(node, ast.ImportFrom):
                if node.module in self.DANGEROUS_MODULES:
                    issues.append(SecurityIssue(
                        risk_level=RiskLevel.HIGH,
                        message=f"Dangerous module import: {node.module}",
                        line_number=node.lineno,
                        suggestion=f"Remove import from {node.module} or use safer alternatives"
                    ))
        
        return issues
    
    def _check_function_calls(self, tree: ast.AST) -> List[SecurityIssue]:
        """Check for dangerous function calls"""
        issues = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Call):
                func_name = self._get_function_name(node.func)
                
                if func_name:
                    # Check for dangerous built-in functions
                    if func_name in ['eval', 'exec', 'compile', '__import__']:
                        issues.append(SecurityIssue(
                            risk_level=RiskLevel.CRITICAL,
                            message=f"Dangerous function call: {func_name}",
                            line_number=node.lineno,
                            suggestion=f"Remove {func_name} call or use safer alternatives"
                        ))
                    
                    # Check for file operations
                    elif func_name in ['open', 'file']:
                        issues.append(SecurityIssue(
                            risk_level=RiskLevel.MEDIUM,
                            message=f"File operation detected: {func_name}",
                            line_number=node.lineno,
                            suggestion="Ensure file operations are necessary and safe"
                        ))
                    
                    # Check for Blender quit operations
                    elif 'quit' in func_name.lower():
                        issues.append(SecurityIssue(
                            risk_level=RiskLevel.HIGH,
                            message="Blender quit operation detected",
                            line_number=node.lineno,
                            suggestion="Remove quit operation to prevent unexpected Blender closure"
                        ))
        
        return issues
    
    def _check_patterns(self, code: str) -> List[SecurityIssue]:
        """Check for dangerous patterns using regex"""
        issues = []
        lines = code.split('\n')
        
        for i, line in enumerate(lines, 1):
            for pattern in self.DANGEROUS_PATTERNS:
                if re.search(pattern, line, re.IGNORECASE):
                    issues.append(SecurityIssue(
                        risk_level=RiskLevel.MEDIUM,
                        message=f"Potentially dangerous pattern detected: {pattern}",
                        line_number=i,
                        suggestion="Review this line for potential security issues"
                    ))
        
        return issues
    
    def _check_file_operations(self, tree: ast.AST) -> List[SecurityIssue]:
        """Check for file operations"""
        issues = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Call):
                func_name = self._get_function_name(node.func)
                
                if func_name and any(op in func_name.lower() for op in ['save', 'export', 'write']):
                    issues.append(SecurityIssue(
                        risk_level=RiskLevel.MEDIUM,
                        message=f"File operation detected: {func_name}",
                        line_number=node.lineno,
                        suggestion="Ensure file operations are intended and safe"
                    ))
        
        return issues
    
    def _check_network_operations(self, tree: ast.AST) -> List[SecurityIssue]:
        """Check for network operations"""
        issues = []
        
        network_keywords = ['http', 'url', 'request', 'socket', 'ftp', 'smtp']
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Call):
                func_name = self._get_function_name(node.func)
                
                if func_name and any(keyword in func_name.lower() for keyword in network_keywords):
                    issues.append(SecurityIssue(
                        risk_level=RiskLevel.HIGH,
                        message=f"Network operation detected: {func_name}",
                        line_number=node.lineno,
                        suggestion="Network operations require explicit user permission"
                    ))
        
        return issues
    
    def _get_function_name(self, node: ast.AST) -> Optional[str]:
        """Extract function name from AST node"""
        if isinstance(node, ast.Name):
            return node.id
        elif isinstance(node, ast.Attribute):
            value = self._get_function_name(node.value)
            if value:
                return f"{value}.{node.attr}"
            return node.attr
        return None
    
    def _determine_safety(self, issues: List[SecurityIssue]) -> bool:
        """Determine if code is safe based on security level and issues"""
        if not issues:
            return True
        
        critical_issues = [i for i in issues if i.risk_level == RiskLevel.CRITICAL]
        high_issues = [i for i in issues if i.risk_level == RiskLevel.HIGH]
        
        if self.security_level == SecurityLevel.STRICT:
            # Block any medium or higher risk
            return not any(i.risk_level in [RiskLevel.MEDIUM, RiskLevel.HIGH, RiskLevel.CRITICAL] for i in issues)
        
        elif self.security_level == SecurityLevel.MODERATE:
            # Block critical and high risk
            return not (critical_issues or high_issues)
        
        else:  # PERMISSIVE
            # Only block critical issues
            return not critical_issues


class SafeExecutor:
    """Safe code execution with sandboxing"""
    
    def __init__(self, validator: Optional[CodeValidator] = None):
        self.validator = validator or CodeValidator()
        self.logger = get_logger(f"{__name__}.SafeExecutor")
    
    def execute_code(self, code: str, context: Optional[Dict] = None) -> Tuple[bool, Optional[str]]:
        """
        Safely execute code with validation
        
        Args:
            code: Python code to execute
            context: Optional execution context
            
        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Validate code first
            is_safe, issues = self.validator.validate_code(code)
            
            if not is_safe:
                error_msg = "Code validation failed:\n"
                for issue in issues:
                    error_msg += f"- {issue.message}\n"
                return False, error_msg
            
            # Create safe execution environment
            safe_globals = self._create_safe_globals(context)
            
            # Execute code
            exec(code, safe_globals)
            
            return True, None
            
        except Exception as e:
            error_msg = f"Code execution failed: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg
    
    def _create_safe_globals(self, context: Optional[Dict] = None) -> Dict:
        """Create safe global environment for code execution"""
        safe_globals = {
            '__builtins__': {
                # Safe built-ins only
                'len', 'range', 'enumerate', 'zip', 'map', 'filter',
                'list', 'dict', 'set', 'tuple', 'str', 'int', 'float', 'bool',
                'min', 'max', 'sum', 'abs', 'round', 'sorted', 'reversed',
                'print',  # Allow print for debugging
            },
            'bpy': __import__('bpy'),  # Blender Python API
            'bmesh': __import__('bmesh'),  # Blender mesh utilities
            'mathutils': __import__('mathutils'),  # Math utilities
        }
        
        # Add user context if provided
        if context:
            safe_globals.update(context)
        
        return safe_globals
