"""
AI Manager
==========

Central manager for all AI providers in BlendPro.
Handles provider selection, configuration, and request routing.
"""

from typing import Dict, List, Optional, Type, AsyncGenerator
import asyncio
from enum import Enum

from .base import AIProvider, AIMessage, AIResponse, AIConfig, AIError, AIProviderError
from .openai_provider import OpenAIProvider
from .anthropic_provider import AnthropicProvider
from .google_provider import GoogleProvider
from .custom_provider import CustomProvider, CUSTOM_PROVIDER_PRESETS
from ..utils.logger import get_logger


class ProviderType(Enum):
    """Available AI providers"""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"
    CUSTOM = "custom"


class AIManager:
    """Central manager for AI providers"""
    
    def __init__(self):
        self.logger = get_logger(f"{__name__}.AIManager")
        self._providers: Dict[str, AIProvider] = {}
        self._provider_classes: Dict[str, Type[AIProvider]] = {
            ProviderType.OPENAI.value: OpenAIProvider,
            ProviderType.ANTHROPIC.value: AnthropicProvider,
            ProviderType.GOOGLE.value: GoogleProvider,
            ProviderType.CUSTOM.value: CustomProvider,
        }
        self._active_provider: Optional[str] = None
        
    def register_provider(self, provider_name: str, api_key: str, config: Optional[AIConfig] = None) -> bool:
        """
        Register a new AI provider
        
        Args:
            provider_name: Name of the provider (openai, anthropic, google)
            api_key: API key for the provider
            config: Optional configuration
            
        Returns:
            True if registration successful, False otherwise
        """
        try:
            if provider_name not in self._provider_classes:
                self.logger.error(f"Unknown provider: {provider_name}")
                return False
            
            provider_class = self._provider_classes[provider_name]
            provider = provider_class(api_key, config)
            
            # Validate API key
            if not provider.validate_api_key():
                self.logger.error(f"Invalid API key for provider: {provider_name}")
                return False
            
            self._providers[provider_name] = provider
            
            # Set as active if it's the first provider
            if self._active_provider is None:
                self._active_provider = provider_name
            
            self.logger.info(f"Provider registered successfully: {provider_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to register provider {provider_name}: {e}")
            return False
    
    def set_active_provider(self, provider_name: str) -> bool:
        """
        Set the active AI provider
        
        Args:
            provider_name: Name of the provider to set as active
            
        Returns:
            True if successful, False otherwise
        """
        if provider_name not in self._providers:
            self.logger.error(f"Provider not registered: {provider_name}")
            return False
        
        self._active_provider = provider_name
        self.logger.info(f"Active provider set to: {provider_name}")
        return True
    
    def get_active_provider(self) -> Optional[AIProvider]:
        """Get the currently active AI provider"""
        if self._active_provider and self._active_provider in self._providers:
            return self._providers[self._active_provider]
        return None
    
    def get_provider(self, provider_name: str) -> Optional[AIProvider]:
        """Get a specific AI provider by name"""
        return self._providers.get(provider_name)
    
    def get_available_providers(self) -> List[str]:
        """Get list of registered provider names"""
        return list(self._providers.keys())
    
    async def generate_response(
        self, 
        messages: List[AIMessage], 
        provider_name: Optional[str] = None,
        config: Optional[AIConfig] = None
    ) -> AIResponse:
        """
        Generate a response using specified or active provider
        
        Args:
            messages: List of conversation messages
            provider_name: Specific provider to use, or None for active provider
            config: Optional configuration override
            
        Returns:
            AI response
            
        Raises:
            AIProviderError: If no provider available or generation fails
        """
        # Determine which provider to use
        if provider_name:
            provider = self.get_provider(provider_name)
            if not provider:
                raise AIProviderError(f"Provider not available: {provider_name}")
        else:
            provider = self.get_active_provider()
            if not provider:
                raise AIProviderError("No active provider available")
        
        try:
            self.logger.debug(f"Generating response with provider: {provider.name}")
            response = await provider.generate_response(messages, config)
            self.logger.debug(f"Response generated successfully")
            return response
            
        except Exception as e:
            self.logger.error(f"Failed to generate response: {e}")
            raise
    
    def register_custom_provider(
        self,
        provider_name: str,
        base_url: str,
        api_key: str = "",
        config: Optional[AIConfig] = None,
        preset: Optional[str] = None
    ) -> bool:
        """
        Register a custom OpenAI-compatible provider

        Args:
            provider_name: Custom name for the provider
            base_url: Base URL for the API endpoint
            api_key: API key (optional for local providers)
            config: Optional configuration
            preset: Optional preset name from CUSTOM_PROVIDER_PRESETS

        Returns:
            True if registration successful, False otherwise
        """
        try:
            # Use preset if provided
            if preset and preset in CUSTOM_PROVIDER_PRESETS:
                preset_config = CUSTOM_PROVIDER_PRESETS[preset]
                if not base_url:
                    base_url = preset_config["base_url"]
                if not config:
                    config = AIConfig(model=preset_config["default_model"])

            # Create custom provider
            provider = CustomProvider(
                api_key=api_key,
                config=config,
                base_url=base_url,
                provider_name=provider_name
            )

            # Validate connection
            if not provider.validate_api_key():
                self.logger.error(f"Invalid connection for custom provider: {provider_name}")
                return False

            self._providers[provider_name] = provider

            # Set as active if it's the first provider
            if self._active_provider is None:
                self._active_provider = provider_name

            self.logger.info(f"Custom provider registered successfully: {provider_name} at {base_url}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to register custom provider {provider_name}: {e}")
            return False

    def get_custom_provider_presets(self) -> Dict[str, Dict]:
        """Get available custom provider presets"""
        return CUSTOM_PROVIDER_PRESETS.copy()

    def refresh_provider_models(self, provider_name: str) -> bool:
        """Refresh models for a custom provider"""
        try:
            provider = self.get_provider(provider_name)
            if provider and isinstance(provider, CustomProvider):
                models = provider.refresh_models()
                self.logger.info(f"Refreshed {len(models)} models for {provider_name}")
                return True
            return False

        except Exception as e:
            self.logger.error(f"Failed to refresh models for {provider_name}: {e}")
            return False

    def add_custom_model(self, provider_name: str, model_id: str) -> bool:
        """Add a custom model to a provider"""
        try:
            provider = self.get_provider(provider_name)
            if provider and isinstance(provider, CustomProvider):
                provider.add_custom_model(model_id)
                self.logger.info(f"Added custom model {model_id} to {provider_name}")
                return True
            return False

        except Exception as e:
            self.logger.error(f"Failed to add custom model: {e}")
            return False

    def cleanup(self):
        """Cleanup all providers and resources"""
        try:
            self.logger.info("Cleaning up AI Manager...")
            self._providers.clear()
            self._active_provider = None
            self.logger.info("AI Manager cleanup completed")

        except Exception as e:
            self.logger.error(f"Error during AI Manager cleanup: {e}")
