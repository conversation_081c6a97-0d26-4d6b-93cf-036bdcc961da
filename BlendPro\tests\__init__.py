"""
BlendPro Tests
=============

Test suite for BlendPro addon including unit tests, integration tests,
and Blender-specific tests.
"""

import sys
import os
from pathlib import Path

# Add BlendPro package to path for testing
test_dir = Path(__file__).parent
project_dir = test_dir.parent
blendpro_dir = project_dir / "blendpro"

if str(blendpro_dir) not in sys.path:
    sys.path.insert(0, str(blendpro_dir))

# Test configuration
TEST_CONFIG = {
    "mock_api_keys": {
        "openai": "test-openai-key",
        "anthropic": "test-anthropic-key", 
        "google": "test-google-key",
    },
    "test_timeout": 5,  # seconds
    "enable_network_tests": False,  # Set to True for integration tests
}
