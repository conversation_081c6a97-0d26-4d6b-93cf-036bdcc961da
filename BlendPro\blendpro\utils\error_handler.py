"""
BlendPro Error Handler
=====================

Comprehensive error handling system for BlendPro with user-friendly
error messages, recovery suggestions, and error reporting.
"""

import traceback
import sys
from typing import Optional, Dict, Any, List, Callable
from dataclasses import dataclass
from enum import Enum
from datetime import datetime
import bpy

from .logger import get_logger


class ErrorCategory(Enum):
    """Categories of errors"""
    AI_PROVIDER = "ai_provider"
    CODE_EXECUTION = "code_execution"
    NETWORK = "network"
    CONFIGURATION = "configuration"
    UI = "ui"
    SECURITY = "security"
    BLENDER_API = "blender_api"
    UNKNOWN = "unknown"


class ErrorSeverity(Enum):
    """Error severity levels"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class BlendProError:
    """Structured error information"""
    category: ErrorCategory
    severity: ErrorSeverity
    message: str
    technical_details: Optional[str] = None
    user_message: Optional[str] = None
    suggestions: Optional[List[str]] = None
    timestamp: Optional[datetime] = None
    context: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
        
        if self.user_message is None:
            self.user_message = self._generate_user_message()
        
        if self.suggestions is None:
            self.suggestions = self._generate_suggestions()
    
    def _generate_user_message(self) -> str:
        """Generate user-friendly error message"""
        category_messages = {
            ErrorCategory.AI_PROVIDER: "AI service error",
            ErrorCategory.CODE_EXECUTION: "Code execution error",
            ErrorCategory.NETWORK: "Network connection error",
            ErrorCategory.CONFIGURATION: "Configuration error",
            ErrorCategory.UI: "Interface error",
            ErrorCategory.SECURITY: "Security validation error",
            ErrorCategory.BLENDER_API: "Blender API error",
            ErrorCategory.UNKNOWN: "Unexpected error",
        }
        
        base_message = category_messages.get(self.category, "Error occurred")
        return f"{base_message}: {self.message}"
    
    def _generate_suggestions(self) -> List[str]:
        """Generate recovery suggestions based on error category"""
        suggestions_map = {
            ErrorCategory.AI_PROVIDER: [
                "Check your API key in preferences",
                "Verify internet connection",
                "Try a different AI model",
                "Check API service status"
            ],
            ErrorCategory.CODE_EXECUTION: [
                "Review the generated code for errors",
                "Check if required objects exist in scene",
                "Ensure proper object selection",
                "Try simpler commands first"
            ],
            ErrorCategory.NETWORK: [
                "Check internet connection",
                "Verify firewall settings",
                "Try again in a few moments",
                "Check proxy settings if applicable"
            ],
            ErrorCategory.CONFIGURATION: [
                "Check addon preferences",
                "Reset settings to defaults",
                "Reinstall the addon if needed",
                "Check Blender version compatibility"
            ],
            ErrorCategory.UI: [
                "Restart Blender",
                "Reset UI layout",
                "Check for addon conflicts",
                "Update Blender to latest version"
            ],
            ErrorCategory.SECURITY: [
                "Review code for dangerous operations",
                "Adjust security settings if needed",
                "Use safer alternatives",
                "Contact support if code seems safe"
            ],
            ErrorCategory.BLENDER_API: [
                "Check Blender version compatibility",
                "Ensure objects are selected properly",
                "Switch to appropriate mode/context",
                "Check object types and properties"
            ],
        }
        
        return suggestions_map.get(self.category, [
            "Try the operation again",
            "Restart Blender if problem persists",
            "Check addon documentation",
            "Report issue if problem continues"
        ])


class ErrorHandler:
    """Central error handling system"""
    
    def __init__(self):
        self.logger = get_logger(f"{__name__}.ErrorHandler")
        self.error_history: List[BlendProError] = []
        self.max_history = 100
        self.error_callbacks: Dict[ErrorCategory, List[Callable]] = {}
    
    def handle_error(
        self, 
        exception: Exception, 
        category: ErrorCategory = ErrorCategory.UNKNOWN,
        context: Optional[Dict[str, Any]] = None,
        user_message: Optional[str] = None
    ) -> BlendProError:
        """
        Handle an error and create structured error information
        
        Args:
            exception: The exception that occurred
            category: Category of the error
            context: Additional context information
            user_message: Custom user-friendly message
            
        Returns:
            Structured error information
        """
        # Determine severity
        severity = self._determine_severity(exception, category)
        
        # Create error object
        error = BlendProError(
            category=category,
            severity=severity,
            message=str(exception),
            technical_details=self._get_technical_details(exception),
            user_message=user_message,
            context=context
        )
        
        # Log the error
        self._log_error(error)
        
        # Add to history
        self._add_to_history(error)
        
        # Execute callbacks
        self._execute_callbacks(error)
        
        # Show user notification if appropriate
        self._show_user_notification(error)
        
        return error
    
    def handle_ai_error(self, exception: Exception, provider: str = "unknown") -> BlendProError:
        """Handle AI provider specific errors"""
        context = {"provider": provider}
        
        # Categorize AI errors
        error_msg = str(exception).lower()
        
        if "api key" in error_msg or "unauthorized" in error_msg:
            user_message = f"Invalid API key for {provider}. Please check your API key in preferences."
        elif "rate limit" in error_msg or "quota" in error_msg:
            user_message = f"Rate limit exceeded for {provider}. Please wait before trying again."
        elif "timeout" in error_msg:
            user_message = f"Request to {provider} timed out. Please check your connection and try again."
        elif "network" in error_msg or "connection" in error_msg:
            user_message = f"Network error connecting to {provider}. Please check your internet connection."
        else:
            user_message = f"Error communicating with {provider}. Please try again."
        
        return self.handle_error(
            exception, 
            ErrorCategory.AI_PROVIDER, 
            context, 
            user_message
        )
    
    def handle_code_execution_error(self, exception: Exception, code: str = "") -> BlendProError:
        """Handle code execution specific errors"""
        context = {"code": code[:500]}  # First 500 chars of code
        
        # Categorize execution errors
        error_msg = str(exception).lower()
        
        if "syntax" in error_msg:
            user_message = "Generated code has syntax errors. Please try rephrasing your request."
        elif "attribute" in error_msg:
            user_message = "Code tried to access non-existent properties. Check object selection."
        elif "index" in error_msg:
            user_message = "Code tried to access invalid indices. Check object counts."
        elif "key" in error_msg:
            user_message = "Code tried to access missing data. Check object properties."
        elif "context" in error_msg:
            user_message = "Code requires different Blender context. Try switching modes."
        else:
            user_message = "Code execution failed. Please try a simpler request."
        
        return self.handle_error(
            exception, 
            ErrorCategory.CODE_EXECUTION, 
            context, 
            user_message
        )
    
    def register_callback(self, category: ErrorCategory, callback: Callable[[BlendProError], None]):
        """Register callback for specific error category"""
        if category not in self.error_callbacks:
            self.error_callbacks[category] = []
        self.error_callbacks[category].append(callback)
    
    def get_error_history(self, category: Optional[ErrorCategory] = None) -> List[BlendProError]:
        """Get error history, optionally filtered by category"""
        if category is None:
            return self.error_history.copy()
        
        return [error for error in self.error_history if error.category == category]
    
    def clear_error_history(self):
        """Clear error history"""
        self.error_history.clear()
        self.logger.info("Error history cleared")
    
    def get_error_stats(self) -> Dict[str, Any]:
        """Get error statistics"""
        if not self.error_history:
            return {"total": 0}
        
        stats = {
            "total": len(self.error_history),
            "by_category": {},
            "by_severity": {},
            "recent_errors": len([e for e in self.error_history if 
                                (datetime.now() - e.timestamp).seconds < 3600])  # Last hour
        }
        
        for error in self.error_history:
            # Count by category
            cat_name = error.category.value
            stats["by_category"][cat_name] = stats["by_category"].get(cat_name, 0) + 1
            
            # Count by severity
            sev_name = error.severity.value
            stats["by_severity"][sev_name] = stats["by_severity"].get(sev_name, 0) + 1
        
        return stats
    
    def _determine_severity(self, exception: Exception, category: ErrorCategory) -> ErrorSeverity:
        """Determine error severity"""
        # Critical errors
        if isinstance(exception, (SystemExit, KeyboardInterrupt)):
            return ErrorSeverity.CRITICAL
        
        # Category-based severity
        if category == ErrorCategory.SECURITY:
            return ErrorSeverity.ERROR
        elif category == ErrorCategory.CONFIGURATION:
            return ErrorSeverity.WARNING
        elif category == ErrorCategory.UI:
            return ErrorSeverity.INFO
        
        # Exception type-based severity
        if isinstance(exception, (ValueError, TypeError, AttributeError)):
            return ErrorSeverity.ERROR
        elif isinstance(exception, (ConnectionError, TimeoutError)):
            return ErrorSeverity.WARNING
        
        return ErrorSeverity.ERROR
    
    def _get_technical_details(self, exception: Exception) -> str:
        """Get technical details including traceback"""
        return traceback.format_exc()
    
    def _log_error(self, error: BlendProError):
        """Log error with appropriate level"""
        log_message = f"[{error.category.value}] {error.message}"
        
        if error.severity == ErrorSeverity.CRITICAL:
            self.logger.critical(log_message)
        elif error.severity == ErrorSeverity.ERROR:
            self.logger.error(log_message)
        elif error.severity == ErrorSeverity.WARNING:
            self.logger.warning(log_message)
        else:
            self.logger.info(log_message)
        
        # Log technical details at debug level
        if error.technical_details:
            self.logger.debug(f"Technical details: {error.technical_details}")
    
    def _add_to_history(self, error: BlendProError):
        """Add error to history with size limit"""
        self.error_history.append(error)
        
        # Limit history size
        if len(self.error_history) > self.max_history:
            self.error_history = self.error_history[-self.max_history:]
    
    def _execute_callbacks(self, error: BlendProError):
        """Execute registered callbacks for error category"""
        callbacks = self.error_callbacks.get(error.category, [])
        
        for callback in callbacks:
            try:
                callback(error)
            except Exception as e:
                self.logger.error(f"Error in error callback: {e}")
    
    def _show_user_notification(self, error: BlendProError):
        """Show user notification for appropriate errors"""
        if error.severity in [ErrorSeverity.ERROR, ErrorSeverity.CRITICAL]:
            # Use Blender's report system
            try:
                if hasattr(bpy.context, 'window_manager'):
                    # This would be called from an operator
                    pass
            except Exception:
                # Fallback to console
                print(f"BlendPro Error: {error.user_message}")


# Global error handler instance
_error_handler: Optional[ErrorHandler] = None


def get_error_handler() -> ErrorHandler:
    """Get global error handler instance"""
    global _error_handler
    
    if _error_handler is None:
        _error_handler = ErrorHandler()
    
    return _error_handler


def handle_error(exception: Exception, category: ErrorCategory = ErrorCategory.UNKNOWN, **kwargs) -> BlendProError:
    """Convenience function to handle errors"""
    return get_error_handler().handle_error(exception, category, **kwargs)
